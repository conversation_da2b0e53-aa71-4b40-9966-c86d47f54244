package com.mall.project.service.quantifyEvolve.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mall.common.api.CommonPage;
import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.quantifyEvolve.QuantifyEvolveDao;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.quantifyEvolve.QuantifyEvolveService;
import com.mall.project.util.MallBAuthUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 量化值进化量服务实现类
 */
@Service
@Slf4j
public class QuantifyEvolveServiceImpl implements QuantifyEvolveService {

    @Autowired
    private QuantifyEvolveDao quantifyEvolveDao;

    @Autowired
    private MallBAuthUtils mallBAuthUtils;

    @Override
    public void updateQuantifyEvolve() {
        quantifyEvolveDao.updateQuantifyEvolve();
    }

    @Override
    public CommonPage<Map<String, Object>> queryQuantifyEvolvePages(String phone, String startDate, String endDate, int pageNum, int pageSize) {
        // 验证开始日期格式 yyyy-MM-dd
        if (startDate != null && !startDate.isEmpty() && !startDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new RuntimeException("开始时间格式不正确，请输入yyyy-MM-dd格式");
        }
        // 验证结束日期格式 yyyy-MM-dd
        if (endDate != null && !endDate.isEmpty() && !endDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new RuntimeException("结束时间格式不正确，请输入yyyy-MM-dd格式");
        }
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }
        int offset = (pageNum - 1) * pageSize;
        List<Map<String, Object>> dataList = quantifyEvolveDao.queryQuantifyEvolvePages(phone, startDate, endDate, pageSize, offset);
        // 转换下划线格式为驼峰格式
        List<Map<String, Object>> formattedTradeDataSet = dataList.stream().map(ConvertToCamelCase::convertToCamelCase).toList();
        long total = quantifyEvolveDao.totalQuantifyEvolve(phone, startDate, endDate);
        int totalPages = (total == 0) ? 0 : (int) Math.ceil((double) total / pageSize);
        QuantifyEvolveServiceImpl.CustomCommonPage<Map<String, Object>> commonPage = new QuantifyEvolveServiceImpl.CustomCommonPage<>(pageNum, pageSize, totalPages, total, formattedTradeDataSet);
        // 添加汇总数据到返回结果中
        Map<String, Object> summary = new HashMap<>();
        summary.put("todayTotalQuantifyEvolve", todayTotalQuantifyEvolve(startDate));   //今日量化值进化量
        summary.put("totalQuantifyEvolve", totalQuantifyEvolve(startDate));   //累计量化值进化量
        commonPage.setSummary(summary);
        return commonPage;
    }

    // 新增内部类扩展CommonPage
    @Setter
    @Getter
    private static class CustomCommonPage<T> extends CommonPage<T> {
        private Map<String, Object> summary;
        public CustomCommonPage(int pageNum, int pageSize, int totalPage, long total, List<T> list) {
            super(pageNum, pageSize, totalPage, total, list);
        }
    }


    @Override
    public List<Map<String, Object>> exportQuantifyEvolveExcel(String phone, String startDate, String endDate) {
        // 验证开始日期格式 yyyy-MM-dd
        if (startDate != null && !startDate.isEmpty() && !startDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new RuntimeException("开始时间格式不正确，请输入yyyy-MM-dd格式");
        }
        // 验证结束日期格式 yyyy-MM-dd
        if (endDate != null && !endDate.isEmpty() && !endDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new RuntimeException("结束时间格式不正确，请输入yyyy-MM-dd格式");
        }
        // 转换下划线格式为驼峰格式
        return quantifyEvolveDao.exportQuantifyEvolveExcel(phone,startDate, endDate).stream().map(ConvertToCamelCase::convertToCamelCase).toList();
    }

    @Override
    public String todayTotalQuantifyEvolve(String startTime) {
        return quantifyEvolveDao.todayTotalQuantifyEvolve(startTime) == null ? "0" : quantifyEvolveDao.todayTotalQuantifyEvolve(startTime);
    }

    @Override
    public String totalQuantifyEvolve(String startTime) {
        return quantifyEvolveDao.totalQuantifyEvolve(startTime) == null ? "0" : quantifyEvolveDao.totalQuantifyEvolve(startTime);
    }

    /**
     * 从mallB系统读取用户类型为B的量化值进化量
     */
    @Override
    public void getQuantifyEvolveFromMallB() {
        try {
            // 创建ObjectMapper用于JSON处理
            ObjectMapper objectMapper = new ObjectMapper();

            // 使用工具类发送带参数的GET请求到mallB系统获取量化值进化量数据
            ResponseEntity<String> quantifyEvolveResponse = mallBAuthUtils.getForEntity("/mall/receptionA/statistics/quantification", String.class);

            // 检查获取量化值进化量数据是否成功
            if (quantifyEvolveResponse.getStatusCode() != HttpStatus.OK) {
                throw new BusinessException("获取mallB系统量化值进化量数据失败: " + quantifyEvolveResponse.getStatusCode());
            }

            // 解析量化值进化量数据响应
            String quantifyEvolveResponseBody = quantifyEvolveResponse.getBody();
            if (quantifyEvolveResponseBody == null) {
                throw new BusinessException("获取mallB系统量化值进化量数据响应为空");
            }

            // 解析响应JSON
            JsonNode quantifyEvolveRoot = objectMapper.readTree(quantifyEvolveResponseBody);

            if (quantifyEvolveRoot.get("code").asInt() != 200) {
                throw new BusinessException("获取mallB系统量化值进化量数据失败: " + quantifyEvolveRoot.get("msg").asText());
            }

            // 获取量化值进化量数据列表
            JsonNode quantifyEvolveList = quantifyEvolveRoot.path("rows");
            if (!quantifyEvolveList.isArray() || quantifyEvolveList.isEmpty()) {
                log.info("今日mallB系统无量化值进化量数据");
                return;
            }
            // 存储量化值进化量数据到本地系统
            log.info("开始处理并存储mallB量化值进化量数据，共 {} 条记录", quantifyEvolveList.size());

            // 遍历量化值进化量数据并存储
            for (JsonNode evolveItem : quantifyEvolveList) {
                try {
                    String phone = evolveItem.path("phone").asText();
                    String quantifyEvolve = evolveItem.path("dayConvertAmount").asText();
                    String updateDate = evolveItem.path("date").asText();
                    // 调用DAO层存储数据
                    quantifyEvolveDao.saveMallBQuantifyEvolveData(phone, quantifyEvolve, updateDate);

                } catch (Exception e) {
                    log.error("处理mallB量化值进化量数据项失败: {}", e.getMessage(), e);
                    return;
                }
            }
            log.info("成功完成mallB系统量化值进化量数据同步");

        } catch (Exception e) {
            log.error("与mallB系统通信失败: {}", e.getMessage(), e);
        }
    }
}
