package com.mall.project.service.quantifyEvolve;

import com.mall.common.api.CommonPage;

import java.util.List;
import java.util.Map;

/**
 * 量化值进化量服务接口
 */
public interface QuantifyEvolveService {

    /**
     * 计算量化值进化量
     */
    public void updateQuantifyEvolve();

    /**
     * 查询量化值进化量, 分页显示
     */
    public CommonPage<Map<String, Object>> queryQuantifyEvolvePages(String phone, String startDate, String endDate, int pageNum, int pageSize);

    /**
     * 量化值进化量, 导出 Excel
     */
    public List<Map<String, Object>> exportQuantifyEvolveExcel(String phone, String startDate, String endDate);

    /**
     * 今日总量化值进化量
     */
    public String todayTotalQuantifyEvolve(String startTime);

    /**
     * 累计量化值进化量
     */
    public String totalQuantifyEvolve(String startTime);

    /**
     * 去mallB系统读取用户类型为B的量化值进化量
     */
    public void getQuantifyEvolveFromMallB();
}
